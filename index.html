<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة رمز التسجيل</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 30px;
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .title {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: bold;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: right;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #555;
            font-weight: 600;
            font-size: 16px;
        }

        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            text-align: center;
            transition: border-color 0.3s ease;
            direction: ltr;
        }

        input[type="text"]:focus, input[type="number"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .result-field {
            background-color: #f8f9fa;
            color: #333;
            font-weight: bold;
            font-size: 18px;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
            transition: transform 0.2s ease;
        }

        .calculate-btn:hover {
            transform: translateY(-2px);
        }

        .calculate-btn:active {
            transform: translateY(0);
        }

        .error {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">حاسبة رمز التسجيل</h1>
        
        <div class="input-group">
            <label for="registrationCode">رمز التسجيل:</label>
            <input type="number" id="registrationCode" placeholder="أدخل رمز التسجيل">
        </div>
        
        <div class="input-group">
            <label for="result">النتيجة:</label>
            <input type="text" id="result" class="result-field" readonly placeholder="النتيجة ستظهر هنا">
        </div>
        
        <button class="calculate-btn" onclick="calculateResult()">احسب النتيجة</button>
        
        <div id="error" class="error"></div>
    </div>

    <script>
        function calculateResult() {
            const registrationCodeInput = document.getElementById('registrationCode');
            const resultInput = document.getElementById('result');
            const errorDiv = document.getElementById('error');
            
            // مسح الأخطاء السابقة
            errorDiv.textContent = '';
            
            const registrationCode = registrationCodeInput.value.trim();
            
            // التحقق من صحة الإدخال
            if (!registrationCode) {
                errorDiv.textContent = 'يرجى إدخال رمز التسجيل';
                return;
            }
            
            if (!/^\d+$/.test(registrationCode)) {
                errorDiv.textContent = 'يرجى إدخال أرقام فقط';
                return;
            }
            
            try {
                // تحويل إلى رقم
                const A3 = parseInt(registrationCode);
                
                // الحصول على أول 3 أرقام من اليسار
                const leftThree = parseInt(registrationCode.substring(0, 3));
                
                // تطبيق المعادلة: (A3 * 98) + (LEFT(A3, 3) * 71)
                const result = (A3 * 98) + (leftThree * 71);
                
                // عرض النتيجة بالأرقام الإنجليزية
                resultInput.value = result.toLocaleString('en-US');
                
            } catch (error) {
                errorDiv.textContent = 'حدث خطأ في الحساب';
            }
        }
        
        // تفعيل الحساب عند الضغط على Enter
        document.getElementById('registrationCode').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                calculateResult();
            }
        });
        
        // حساب تلقائي عند تغيير القيمة
        document.getElementById('registrationCode').addEventListener('input', function() {
            if (this.value.trim()) {
                calculateResult();
            } else {
                document.getElementById('result').value = '';
                document.getElementById('error').textContent = '';
            }
        });
    </script>
</body>
</html>
